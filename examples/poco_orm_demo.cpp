/**
 * @file poco_orm_demo.cpp
 * @brief POCO ORM特性演示程序
 * 
 * 展示如何使用优化后的POCO ORM特性，包括：
 * - 使用bind进行参数绑定
 * - 使用into进行结果绑定
 * - 批量操作
 * - 事务支持
 * - 高级查询功能
 */

#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include "../Database/DatabaseManager.h"
#include "../Database/TestRepository.h"
#include "../Entity/Test.h"

void demonstrateBasicCRUD(TestRepository* repo)
{
    qDebug() << "\n=== 基础CRUD操作演示 ===";
    
    // 创建记录 - 使用bind进行参数绑定
    qDebug() << "1. 创建记录...";
    int id1 = repo->createTest("POCO演示", "展示POCO ORM的基础功能", 100);
    int id2 = repo->createTest("高级特性", "展示POCO ORM的高级特性", 200);
    qDebug() << "创建记录ID:" << id1 << "和" << id2;
    
    // 读取记录 - 使用into进行结果绑定
    qDebug() << "\n2. 读取记录...";
    auto test1 = repo->getTestById(id1);
    if (test1) {
        qDebug() << "读取到记录:" << test1->toString();
    }
    
    // 更新记录 - 使用bind进行参数绑定
    qDebug() << "\n3. 更新记录...";
    bool updated = repo->updateTest(id1, "POCO演示_已更新", "更新后的描述", 150);
    qDebug() << "更新结果:" << (updated ? "成功" : "失败");
    
    // 验证更新
    auto updatedTest = repo->getTestById(id1);
    if (updatedTest) {
        qDebug() << "更新后记录:" << updatedTest->toString();
    }
}

void demonstrateBatchOperations(TestRepository* repo)
{
    qDebug() << "\n=== 批量操作演示 ===";
    
    // 批量创建
    qDebug() << "1. 批量创建记录...";
    QList<Test> testBatch;
    for (int i = 1; i <= 5; ++i) {
        testBatch.append(Test(
            QString("批量测试%1").arg(i),
            QString("这是第%1个批量创建的记录").arg(i),
            i * 50
        ));
    }
    
    int createdCount = repo->batchCreateTests(testBatch);
    qDebug() << "批量创建了" << createdCount << "条记录";
    
    // 获取所有记录验证
    QList<Test> allTests = repo->getAllTests();
    qDebug() << "当前总记录数:" << allTests.size();
    
    // 批量更新
    qDebug() << "\n2. 批量更新记录...";
    QMap<int, Test> updates;
    for (const Test& test : allTests) {
        if (test.getName().startsWith("批量测试")) {
            Test updatedTest = test;
            updatedTest.setName(test.getName() + "_已更新");
            updatedTest.setValue(test.getValue() + 1000);
            updates[test.getId()] = updatedTest;
        }
    }
    
    int updatedCount = repo->batchUpdateTests(updates);
    qDebug() << "批量更新了" << updatedCount << "条记录";
}

void demonstrateAdvancedQueries(TestRepository* repo)
{
    qDebug() << "\n=== 高级查询演示 ===";
    
    // 创建一些测试数据
    repo->createTest("苹果", "水果", 10);
    repo->createTest("香蕉", "水果", 15);
    repo->createTest("苹果派", "甜点", 25);
    repo->createTest("橙子", "水果", 12);
    repo->createTest("蛋糕", "甜点", 30);
    
    // 按值范围查询
    qDebug() << "\n1. 按值范围查询 (10-20)...";
    QList<Test> rangeTests = repo->getTestsByValueRange(10, 20);
    qDebug() << "找到" << rangeTests.size() << "条记录:";
    for (const Test& test : rangeTests) {
        qDebug() << "  -" << test.getName() << ":" << test.getValue();
    }
    
    // 分页查询
    qDebug() << "\n2. 分页查询 (每页3条)...";
    int pageSize = 3;
    int totalCount = repo->getTestCount();
    int totalPages = (totalCount + pageSize - 1) / pageSize;
    
    for (int page = 0; page < totalPages; ++page) {
        QList<Test> pageTests = repo->getTestsPaginated(page * pageSize, pageSize);
        qDebug() << "第" << (page + 1) << "页 (" << pageTests.size() << "条记录):";
        for (const Test& test : pageTests) {
            qDebug() << "  -" << test.getName();
        }
    }
    
    // 按名称搜索
    qDebug() << "\n3. 按名称搜索 (包含'苹果')...";
    QList<Test> appleTests = repo->getTestsByName("苹果");
    qDebug() << "找到" << appleTests.size() << "条包含'苹果'的记录:";
    for (const Test& test : appleTests) {
        qDebug() << "  -" << test.getName();
    }
    
    // 条件计数
    qDebug() << "\n4. 条件计数...";
    int fruitCount = repo->getTestCountByCondition("description = '水果'");
    int dessertCount = repo->getTestCountByCondition("description = '甜点'");
    qDebug() << "水果类记录数:" << fruitCount;
    qDebug() << "甜点类记录数:" << dessertCount;
    
    // 存在性检查
    qDebug() << "\n5. 存在性检查...";
    bool appleExists = repo->existsByName("苹果");
    bool grapeExists = repo->existsByName("葡萄");
    qDebug() << "苹果是否存在:" << (appleExists ? "是" : "否");
    qDebug() << "葡萄是否存在:" << (grapeExists ? "是" : "否");
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== POCO ORM特性演示程序 ===";
    qDebug() << "展示使用bind和into的优化ORM操作";
    
    // 设置数据库
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QString dbPath = tempDir + "/poco_orm_demo.db";
    
    // 删除旧的演示数据库
    if (QFile::exists(dbPath)) {
        QFile::remove(dbPath);
    }
    
    // 初始化数据库
    DatabaseManager dbManager;
    if (!dbManager.initialize(dbPath)) {
        qCritical() << "数据库初始化失败";
        return -1;
    }
    
    if (!dbManager.createTables()) {
        qCritical() << "数据表创建失败";
        return -1;
    }
    
    // 创建Repository（现在只支持异步操作）
    TestRepository repo(&dbManager);
    if (!repo.isValid()) {
        qCritical() << "Repository初始化失败";
        return -1;
    }
    
    try {
        // 演示各种ORM特性
        demonstrateBasicCRUD(&repo);
        demonstrateBatchOperations(&repo);
        demonstrateAdvancedQueries(&repo);
        
        qDebug() << "\n=== 演示完成 ===";
        qDebug() << "最终记录总数:" << repo.getTestCount();
        
    } catch (const std::exception& ex) {
        qCritical() << "演示过程中发生错误:" << ex.what();
        return -1;
    }
    
    // 清理演示数据库
    if (QFile::exists(dbPath)) {
        QFile::remove(dbPath);
        qDebug() << "已清理演示数据库文件";
    }
    
    return 0;
}
