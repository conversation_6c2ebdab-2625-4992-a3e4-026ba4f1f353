project(Poco LANGUAGES CXX)

# 版本 1.14.2

# POCO静态库配置
set(POCO_ROOT ${CMAKE_CURRENT_SOURCE_DIR})
set(POCO_INCLUDE_DIR ${POCO_ROOT}/Include)

# 开发用动态库，发布用静态库

# 创建Poco::Data接口库
add_library(PocoFoundation INTERFACE)

# 创建Poco::Data接口库
add_library(PocoData INTERFACE)

# 创建Poco::Crypto接口库
add_library(PocoCrypto INTERFACE)

# 设置头文件包含路径
target_include_directories(PocoFoundation INTERFACE ${POCO_INCLUDE_DIR})
target_include_directories(PocoData INTERFACE ${POCO_INCLUDE_DIR})
target_include_directories(PocoCrypto INTERFACE ${POCO_INCLUDE_DIR})

# 根据构建类型选择对应的库文件
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    # Debug版本使用动态库
    set(POCO_LIB_DIR ${POCO_ROOT}/Debug/lib)
    set(POCO_FOUNDATION_LIB ${POCO_LIB_DIR}/PocoFoundationd.lib)
    set(POCO_DATA_LIB ${POCO_LIB_DIR}/PocoDatad.lib)
    set(POCO_DATA_SQLITE_LIB ${POCO_LIB_DIR}/PocoDataSQLiteExternal.lib)
    set(POCO_Crypto_LIB ${POCO_LIB_DIR}/PocoCryptod.lib)
else()
    # Release版本使用静态库
    set(POCO_LIB_DIR ${POCO_ROOT}/Release)
    set(POCO_FOUNDATION_LIB ${POCO_LIB_DIR}/PocoFoundationmd.lib)
    set(POCO_DATA_LIB ${POCO_LIB_DIR}/PocoDatamd.lib)
    set(POCO_DATA_SQLITE_LIB ${POCO_LIB_DIR}/PocoDataSQLiteExternal.lib)
    set(POCO_Crypto_LIB ${POCO_LIB_DIR}/PocoCryptomd.lib)
endif()

# 链接库文件
target_link_libraries(PocoFoundation
        INTERFACE
        ${POCO_FOUNDATION_LIB}
)
target_link_libraries(PocoData
        INTERFACE
        SQLCipher          # SQLCipher 必须在 POCO SQLite 之前链接
        PocoFoundation
        ${POCO_DATA_LIB}
        ${POCO_DATA_SQLITE_LIB}
        OpenSSL
)
target_link_libraries(PocoCrypto
        INTERFACE
        PocoFoundation
        ${POCO_Crypto_LIB}
        OpenSSL
)

# 在Windows平台上添加必需的系统库
if(WIN32)
    target_link_libraries(PocoData
        INTERFACE
        iphlpapi    # IP Helper API - 用于GetAdaptersAddresses等函数
        ws2_32      # Windows Sockets API
        winmm       # Windows Multimedia API
        crypt32     # Windows Cryptography API
        wldap32     # Windows LDAP API
    )
endif()

# 设置POCO相关的预处理器定义
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    # Debug版本使用动态库，不定义POCO_STATIC
    target_compile_definitions(PocoData
        INTERFACE
        POCO_NO_AUTOMATIC_LIBS
    )
else()
    # Release版本使用静态库，定义POCO_STATIC
    target_compile_definitions(PocoData
        INTERFACE
        POCO_STATIC
        POCO_NO_AUTOMATIC_LIBS
    )
endif()

# 定义DLL复制函数
function(add_poco_dll_copy target_name)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        # Debug版本需要复制DLL文件到输出目录
        # 使用POCO_ROOT变量确保路径正确
        set(POCO_BIN_DIR "${CMAKE_SOURCE_DIR}/Thirdparty/Poco/Debug/bin")
        # 检查DLL文件是否存在
        if(NOT EXISTS "${POCO_BIN_DIR}/PocoFoundation64d.dll")
            message(WARNING "POCO DLL文件不存在: ${POCO_BIN_DIR}/PocoFoundation64d.dll")
            return()
        endif()

        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/PocoFoundation64d.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/PocoData64d.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/PocoDataSQLiteExternal.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/pcre2-8.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/utf8proc.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/zlib1.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${POCO_BIN_DIR}/PocoCrypto64d.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMENT "Copying POCO DLL files to ${target_name} output directory"
        )

        message(STATUS "已为目标 ${target_name} 配置POCO DLL复制，源路径: ${POCO_BIN_DIR}")
    endif()
endfunction()
